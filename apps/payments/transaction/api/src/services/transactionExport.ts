import { S3Client } from '@npco/component-bff-core/dist/aws/s3Client';
import { ServerError } from '@npco/component-bff-core/dist/error';
import { info, error } from '@npco/component-bff-core/dist/utils/logger';
import { EnvironmentService } from '@npco/component-dbs-mp-common/dist/config';
import { DynamodbService } from '@npco/component-dbs-mp-common/dist/dynamodb/dynamodbService';
import { ExcelWriter } from '@npco/component-dbs-mp-common/dist/interface/excelWriter';
import type { Column } from '@npco/component-dbs-mp-common/dist/interface/excelWriter';
import { TransactionInvoicePdfService } from '@npco/component-dbs-mp-common/dist/pdf/transactionInvoicePdfService';
import type { EntityGsiKeySchema } from '@npco/component-dbs-mp-common/dist/types';
import { ExportType } from '@npco/component-dbs-mp-common/dist/types';
import {
  findTimeoffsetFromRequest,
  getStartAndEndFromFilter,
  dateTimeFormatForOffset,
} from '@npco/component-dbs-mp-common/dist/utils/pdfExportUtils';
import { TransactionType, DbRecordType } from '@npco/component-dto-core/dist/types';
import type { Deposit } from '@npco/component-dto-deposit/dist/types';
import type {
  TransactionQueryInput,
  TransactionExportRow,
  Transaction,
} from '@npco/component-dto-transaction/dist/types';

import type { PutObjectCommandInput } from '@aws-sdk/client-s3';
import { Injectable } from '@nestjs/common';
import type { Readable } from 'stream';

import { TransactionExportFormatter } from './transactionExportFormatter';
import { TransactionService } from './transactionService';
import { parseExternalReference } from './transactionUtils';
import type { TransactionConnection, TransactionFields } from './types';
import { TRANSACTION_HEADER_COLUMNS, AMOUNT_COLUMNS, TRANSACTION_COLUMNS } from './utils';

@Injectable()
export class TransactionExportService {
  private static readonly COLUMN_WIDTH = 12;

  private static readonly EXPIRES_IN = 365 * 24 * 60 * 60 * 1000; // file exists in s3 for 1 year

  private static readonly PRE_SIGNED_EXPIRES_IN = 60 * 60; // in seconds

  private static readonly AMOUNT_COLUMNS = AMOUNT_COLUMNS;

  private readonly s3Client: S3Client;

  constructor(
    protected readonly envService: EnvironmentService,
    protected readonly transactionService: TransactionService,
    protected readonly pdfService: TransactionInvoicePdfService,
    protected readonly dynamodbService: DynamodbService,
  ) {
    this.s3Client = new S3Client(envService.awsRegion);
  }

  exportTransaction = async (
    event: TransactionQueryInput,
    entityUuid: string,
    exportType: ExportType,
    managerCustomerUuid?: string,
  ) => {
    const offset = findTimeoffsetFromRequest(event);
    const fileName = `${entityUuid}_${new Date().getTime()}.${exportType.toLowerCase()}`;
    const filePath = 'transaction';

    const key = `${filePath}/${fileName}`;
    try {
      const transactionRows = await this.createTransactionRows(event, entityUuid, offset, managerCustomerUuid);
      info(`export transaction count ${transactionRows.length}`);
      const csvBody = await this.generateExport(transactionRows, exportType, event);
      const expire = await this.uploadToS3(this.s3Client, key, csvBody);
      const presigned = await this.generatePresignedURL(this.s3Client, key);
      return { downloadLink: presigned, expire };
    } catch (err: any) {
      error(err);
      return Promise.reject(new ServerError('Failed to upload transaction.')); // NOSONAR
    }
  };

  uploadToS3 = async (s3: S3Client, key: string, content: Buffer | Readable) => {
    const now = new Date();
    const expires = new Date(now.getTime() + TransactionExportService.EXPIRES_IN);
    const params: PutObjectCommandInput = {
      Body: content,
      Bucket: this.envService.transactionDepositExportBucket,
      Key: key,
      Expires: expires,
    };
    info(`upload ${JSON.stringify(params)}`);
    await s3.upload(params);
    return expires;
  };

  protected readonly createTransactionRows = async (
    event: TransactionQueryInput,
    entityUuid: string,
    offset: string,
    managerCustomerUuid?: string,
  ): Promise<TransactionExportRow[]> => {
    const transactions: Transaction[] = await this.getAllTransactions(event, entityUuid, managerCustomerUuid);
    return this.getTransactionExportRows(transactions, offset);
  };

  protected createTransactionRowColumns(transaction: TransactionFields, offset: string) {
    const { dateStr, time } = TransactionExportFormatter.formatDateAndTime(transaction, offset);

    const gst = transaction.taxAmounts?.find((t: any) => t.name === 'GST');

    return {
      [TRANSACTION_COLUMNS.reference]: transaction.reference,
      [TRANSACTION_COLUMNS.date]: dateStr,
      [TRANSACTION_COLUMNS.time]: time,
      [TRANSACTION_COLUMNS.transactionType]: transaction.type,
      [TRANSACTION_COLUMNS.transactionStatus]: transaction.status,
      [TRANSACTION_COLUMNS.method]: transaction.scheme,
      [TRANSACTION_COLUMNS.last4Digits]: TransactionExportFormatter.last4Digits(transaction),
      [TRANSACTION_COLUMNS.transactionSource]: TransactionExportFormatter.transactionSourceFilter(transaction),
      [TRANSACTION_COLUMNS.site]: transaction.siteName,
      [TRANSACTION_COLUMNS.device]: TransactionExportFormatter.deviceName(transaction),
      [TRANSACTION_COLUMNS.subtotal]: TransactionExportFormatter.formatAmount(transaction.saleAmount, transaction.type),
      [TRANSACTION_COLUMNS.surcharge]: TransactionExportFormatter.formatSurcharge(transaction),
      [TRANSACTION_COLUMNS.tip]: TransactionExportFormatter.formatTip(transaction),
      [TRANSACTION_COLUMNS.gst]: TransactionExportFormatter.formatAmount(gst?.amount),
      [TRANSACTION_COLUMNS.total]: TransactionExportFormatter.formatAmount(transaction.amount, transaction.type),
      [TRANSACTION_COLUMNS.processingFee]: TransactionExportFormatter.formatAmount(
        transaction.feeAmount,
        transaction.type,
      ),
      [TRANSACTION_COLUMNS.posDevice]: transaction.posName,
      [TRANSACTION_COLUMNS.externalReference]: parseExternalReference(
        transaction.source,
        transaction.externalReference,
      ),
      [TRANSACTION_COLUMNS.splitReference]: transaction.splitPaymentUuid,
      [TRANSACTION_COLUMNS.notes]: TransactionExportFormatter.notes(transaction),
      [TRANSACTION_COLUMNS.settlementReference]: TransactionExportFormatter.settlementReference(transaction),
      [TRANSACTION_COLUMNS.originalTransaction]: TransactionExportFormatter.originalTransaction(transaction),
    };
  }

  protected readonly getAllTransactions = async (
    event: TransactionQueryInput,
    entityUuid: string,
    managerCustomerUuid?: string,
  ) => {
    let nextToken: EntityGsiKeySchema | undefined;
    let transactions: Transaction[] = [];
    do {
      try {
        const results: TransactionConnection = await this.transactionService.getTransactions(
          { ...event, limit: 600, nextToken },
          entityUuid,
          managerCustomerUuid,
        );
        nextToken = results.nextToken;
        transactions = transactions.concat(results.transactions);
      } catch (err: any) {
        error(err.message);
        break;
      }
    } while (nextToken);

    return transactions;
  };

  protected readonly getHeaderColumns = () => TRANSACTION_HEADER_COLUMNS;

  private readonly getDepositOrCache = async (
    depositsCache: Map<string, any>,
    entityUuid: string,
    depositUuid?: string,
  ): Promise<Deposit | null> => {
    if (!depositUuid) {
      return null;
    }
    const cachedDeposit = depositsCache.get(depositUuid);
    if (cachedDeposit) {
      return cachedDeposit;
    }

    const response = await this.dynamodbService.queryIdByType(entityUuid, depositUuid, DbRecordType.DEPOSIT);
    const deposit = response.Items?.[0] ?? {};
    depositsCache.set(depositUuid, deposit);
    return deposit as Deposit;
  };

  private readonly addDepositShortIdToTransactions = async (
    depositsCache: Map<string, any>,
    transaction: Transaction,
  ) => {
    const deposit = await this.getDepositOrCache(depositsCache, transaction.entityUuid, transaction.depositUuid);

    if (deposit && (transaction.deposited || deposit.completedAtUtc)) {
      return deposit.shortId;
    }
    return undefined;
  };

  private readonly addOriginalTransactionReference = async (transaction: Transaction) => {
    if (transaction.type === TransactionType.REFUND && transaction.originalTransactionUuid) {
      const originalTransaction = await this.transactionService.getTransaction(
        transaction.entityUuid,
        transaction.originalTransactionUuid,
      );
      return originalTransaction.reference;
    }
    return undefined;
  };

  private readonly getTransactionExportRows = async (
    transactions: Transaction[],
    offset: string,
  ): Promise<TransactionExportRow[]> => {
    const depositsCache = new Map<string, any>();
    const transactionRows: TransactionExportRow[] = [];
    for (const transaction of transactions) {
      const [depositShortId, originalTransactionReference] = await Promise.all([
        this.addDepositShortIdToTransactions(depositsCache, transaction),
        this.addOriginalTransactionReference(transaction),
      ]);
      if (depositShortId) {
        // eslint-disable-next-line
        (transaction as TransactionFields).depositShortId = depositShortId;
      }
      if (originalTransactionReference) {
        // eslint-disable-next-line
        (transaction as TransactionFields).originalTransactionReference = originalTransactionReference;
      }
      transactionRows.push(this.createTransactionRowColumns(transaction, offset) as TransactionExportRow);
    }
    return transactionRows;
  };

  private readonly generateExport = async (
    rows: TransactionExportRow[],
    type: ExportType,
    event: TransactionQueryInput,
  ) => {
    const offset = findTimeoffsetFromRequest(event);
    const [start, end] = getStartAndEndFromFilter(event.timestampFilter);
    const columnHeaders = ExcelWriter.columnsFromColumnNames(
      this.getHeaderColumns(),
      TransactionExportService.COLUMN_WIDTH,
    );
    switch (type) {
      case ExportType.CSV:
        return this.generateCsvExport(columnHeaders, rows);
      case ExportType.XLSX:
        return this.generateXlsxExport(columnHeaders, rows);
      case ExportType.PDF:
        return this.pdfService.createDocument({
          title: `Transactions for ${dateTimeFormatForOffset(start, offset, true)} – ${dateTimeFormatForOffset(
            end,
            offset,
            true,
          )}`,
          items: rows,
          headers: columnHeaders,
        });
      default:
        throw new ServerError('Failed to generate transaction export.');
    }
  };

  private readonly generateCsvExport = (headers: Column[], rows: TransactionExportRow[]) => {
    const writer = new ExcelWriter();

    writer.addSheet(rows, headers);

    return writer.getCsvOutput({ options: { quote: false } });
  };

  private readonly generateXlsxExport = (headers: Column[], rows: TransactionExportRow[]) => {
    const writer = new ExcelWriter();

    const xlsxRows = rows.map((row) => ({ ...row }));

    writer.addSheet(xlsxRows, headers, 'Transactions');
    writer.formatAmountColumns('Transactions', TransactionExportService.AMOUNT_COLUMNS);

    return writer.getXlsxOutput();
  };

  private readonly generatePresignedURL = (s3: S3Client, key: string) => {
    const params = {
      Bucket: this.envService.transactionDepositExportBucket,
      Key: key,
      Expires: TransactionExportService.PRE_SIGNED_EXPIRES_IN,
    };
    info(`generate presigned url: ${JSON.stringify(params)}`);
    return s3.getSignedUrl(params);
  };
}
