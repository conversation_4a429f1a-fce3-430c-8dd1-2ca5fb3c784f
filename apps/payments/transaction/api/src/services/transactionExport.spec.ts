import { S3Client } from '@npco/component-bff-core/dist/aws/s3Client';
import { CardholderService } from '@npco/component-dbs-mp-common/dist/cardholder';
import { EnvironmentService } from '@npco/component-dbs-mp-common/dist/config';
import { DynamodbService } from '@npco/component-dbs-mp-common/dist/dynamodb/dynamodbService';
import { LambdaService } from '@npco/component-dbs-mp-common/dist/lambda';
import { CommonService } from '@npco/component-dbs-mp-common/dist/module/commonService';
import { TransactionInvoicePdfService } from '@npco/component-dbs-mp-common/dist/pdf/transactionInvoicePdfService';
import { SessionService } from '@npco/component-dbs-mp-common/dist/session';
import { ExportType } from '@npco/component-dbs-mp-common/dist/types';
import { adjustToOffset } from '@npco/component-dbs-mp-common/dist/utils/dateUtil';
import {
  getStartAndEndFromFilter,
  dateTimeFormatForOffset,
} from '@npco/component-dbs-mp-common/dist/utils/pdfExportUtils';
import { DbRecordType, TransactionResponseType, TransactionType } from '@npco/component-dto-core/dist/types';
import type { ResponseTransactionCommandDto } from '@npco/component-dto-transaction/dist/responseTransactionCommandDto';
import { CardMedia } from '@npco/component-dto-transaction/dist/types';

import exceljs from 'exceljs';
import fs from 'fs';
import * as path from 'path';
import type { Readable } from 'stream';
import { instance, mock, when } from 'ts-mockito';
import { v4 as uuidv4 } from 'uuid';

import { createTransactionRequestDto } from './testcases/utils';
import { TransactionDb } from './transactionDb';
import { TransactionExportService } from './transactionExport';
import { TransactionService } from './transactionService';
import type { TransactionFields } from './types';
import { TRANSACTION_HEADER_COLUMNS } from './utils';

const baseDir = path.join(__dirname, '..', '..', 'dist', 'services');
jest.mock('@npco/component-dbs-mp-common/dist/utils/sleep');
jest.mock('axios');
jest.mock('aws-xray-sdk-core');
jest.mock('@npco/component-dbs-mp-common/dist/dynamodb/dynamodbService');
jest.mock('@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient');

jest.mock('@npco/component-bff-core/dist/aws/s3Client', () => {
  const mockS3 = {
    getSignedUrl: jest.fn(),
    upload: jest.fn(),
  };
  return { S3Client: jest.fn(() => mockS3) };
});

jest.mock('@npco/component-dbs-mp-common/dist/devices/getDeviceSettingsDbItem', () => {
  return {
    getDeviceSettingsDbItem: jest.fn().mockResolvedValue({
      deviceName: 'not found',
      model: 'NEW9220',
    }),
  };
});
jest.mock('@npco/component-dbs-mp-common/dist/site/getSiteName', () => {
  return {
    getSiteName: jest.fn(() => Promise.resolve(uuidv4().slice(0, 10))),
  };
});

const mockQueryCustomerSiteUuids = jest.fn();
jest.mock('@npco/component-bff-core/dist/middleware/queryCustomerSiteUuids', () => ({
  queryCustomerSiteUuids: () => mockQueryCustomerSiteUuids(),
}));

const expectValidTransactionRows = (
  rows: any[],
  transactions: TransactionFields[],
  { offset, count }: { offset: string; count: number },
) => {
  expect(rows.length).toBe(count);
  rows.forEach((row, i) => {
    const [
      reference,
      date,
      time,
      type,
      status,
      scheme,
      maskedPan,
      source,
      siteName,
      deviceName,
      subtotal,
      surchargeAmount,
      tipAmount,
      gst,
      amount,
      fees,
      posName,
      externalReference,
      splitPaymentUuid,
      notes,
      settlement,
      originalTransaction,
    ] = Object.values(row as any);

    const transaction = transactions[i];
    const tDate = new Date(transaction.timestamp);
    const dateOffset = adjustToOffset(tDate.toISOString(), offset);
    const tGst = transaction.taxAmounts?.find((t: any) => t.name === 'GST');
    const expectedSourceFilter =
      transaction.cardMedia === CardMedia.MANUAL ? `${transaction.sourceFilter}-MOTO` : transaction.sourceFilter;
    const getExpectAmount = (value: number) => `${(value / 100).toFixed(2)}`;
    const expectedOriginalTransaction =
      transaction.type === TransactionType.REFUND ? transaction.originalTransactionUuid : '';

    expect(transaction.reference).toBe(reference);
    expect(transaction.status).toBe(status);

    expect(transaction.maskedPan).toBe(maskedPan);
    expect(transaction.scheme).toBe(scheme);
    expect(transaction.type).toBe(type);
    expect(transaction.siteName).toBe(siteName);
    expect(transaction.deviceName).toBe(deviceName);
    expect(transaction.notes).toBe(notes);
    expect(expectedSourceFilter).toBe(source);
    expect(getExpectAmount(transaction.feeAmount!)).toBe(fees);
    expect(getExpectAmount(transaction.saleAmount)).toBe(subtotal);
    expect(getExpectAmount(transaction.surchargeAmount)).toBe(surchargeAmount);
    expect(getExpectAmount(transaction.tipAmount)).toBe(tipAmount);
    expect(getExpectAmount(transaction.amount)).toBe(amount);
    expect(getExpectAmount(tGst!.amount)).toBe(gst);
    expect(dateOffset.substring(11, 19)).toBe(time);
    expect(dateOffset.substring(0, 10)).toBe(date);
    expect(transaction.posName).toBe(posName);
    expect(transaction.externalReference).toBe(externalReference);
    if (transaction.depositUuid && (settlement as any).length > 0) {
      expect(settlement).toBe('SR123456');
    } else {
      expect(settlement).toBe('');
    }
    expect(transaction.splitPaymentUuid).toBe(splitPaymentUuid);
    expect(expectedOriginalTransaction).toBe(originalTransaction);
  });
};

const compareCsvTransactionReports = (
  body: string,
  transactions: TransactionFields[],
  offset: string,
  count: number,
) => {
  const split = body.split('\n');
  const rowsWithoutHeader = split.slice(1);
  const rows = rowsWithoutHeader.map((r) => r.split(','));

  expectValidTransactionRows(rows, transactions, {
    offset,
    count,
  });
};

const compareXlsxTransactionReports = async (
  body: Buffer,
  transactions: TransactionFields[],
  offset: string,
  count: number,
) => {
  const workbook = new exceljs.Workbook();
  await workbook.xlsx.load(body);

  const sheet = workbook.getWorksheet('Transactions');

  const worksheetValues = sheet!.getSheetValues();
  const [headerRow, ...dataRows] = worksheetValues
    .slice(1, worksheetValues.length) // remove first row as ExcelJs uses null to start counting from 1.
    .map((v) => (Array.isArray(v) ? v.slice(1, v.length) : v)); // remove first column from each row too as it is null.

  const expectedHeaders = TRANSACTION_HEADER_COLUMNS;
  expect(expectedHeaders).toEqual(headerRow);
  expectValidTransactionRows(dataRows, transactions, { offset, count });
};

describe('transaction export test suite', () => {
  let txnExportService: TransactionExportService;
  let txnService: TransactionService;
  let dynamodbSrv: DynamodbService;
  const pdfService = new TransactionInvoicePdfService();
  const mockEnvService: EnvironmentService = mock(EnvironmentService);
  const mockLambdaService: LambdaService = mock(LambdaService);
  const mockCardholderService = mock(CardholderService);
  const mockSessionService = mock(SessionService);
  const env = {
    componentTableName: 'Entities',
    entityGsi: 'entityGsi',
    siteGsi: 'siteGsi',
    transactionDepositExportBucket: 'bucket',
  } as any;

  const mockUpload = jest.fn();
  const mockGetSignedUrl = jest.fn();

  const startDate = new Date('2020-12-21T00:00:00.000Z');
  const endDate = new Date('2021-01-01T23:59:59.000Z');
  const entityUuid = uuidv4();
  const siteUuid = uuidv4();
  const deviceUuid = uuidv4();
  let filter: any;
  const purchaseAmount = 1000;
  const tip = 10;
  const surcharge = 20;
  const tax = 30;
  const shortId = 'SR123456';

  const createTransactionService = () => {
    dynamodbSrv = new DynamodbService(env);
    const service = new TransactionService(
      new CommonService(dynamodbSrv, instance(mockEnvService), instance(mockLambdaService)),
      instance(mockCardholderService),
      instance(mockSessionService),
    );
    service.transactionDb = new TransactionDb(env, dynamodbSrv);
    return service;
  };

  const createExportService = () => {
    const service = new TransactionExportService(env, txnService, pdfService, dynamodbSrv);
    return service;
  };

  const createTransactionResponseDto = (
    entityId: string,
    siteId: string,
    transactionUuid: string,
    timestampUtc: string,
    responseType: TransactionResponseType,
  ): ResponseTransactionCommandDto => ({
    transactionUuid,
    timestampUtc,
    ksn: uuidv4(),
    responseCode: uuidv4(),
    responseDescription: uuidv4(),
    approvalCode: uuidv4(),
    rrn: uuidv4().slice(0, 10),
    panMasked: uuidv4().slice(0, 5),
    panToken: uuidv4(),
    par: uuidv4(),
    cardholderUuid: uuidv4(),
    cardholderEmail: uuidv4(),
    cardholderPhone: uuidv4(),
    responseType,
    deviceUuid: uuidv4(),
    siteUuid: siteId,
    entityUuid: entityId,
    type: TransactionType.PURCHASE,
  });

  const createResponseTransaction = async (
    entityId: string,
    siteId: string,
    deviceId: string,
    amount: number,
    tipAmount: number,
    surchargeAmount: number,
    taxAmount: number,
    timestamp: string,
    timestampUtc: string,
    responseType: TransactionResponseType,
    cardMedia: CardMedia,
  ) => {
    const txn = createTransactionRequestDto();
    txn.entityUuid = entityId;
    txn.deviceUuid = deviceId;
    txn.siteUuid = siteId;
    txn.amount.value = amount;
    txn.tipAmount.value = tipAmount;
    txn.surchargeAmount.value = surchargeAmount;
    txn.type = TransactionType.PURCHASE;
    txn.timestampUtc = timestampUtc;
    txn.timestamp = timestamp;
    txn.taxAmounts[0].amount.value = taxAmount;
    txn.taxAmounts[0].name = 'GST';
    txn.cardMedia = cardMedia;
    await createTransactionService().requestTransactionProjection(txn);
    const response = createTransactionResponseDto(
      txn.entityUuid,
      txn.siteUuid,
      txn.transactionUuid,
      txn.timestampUtc,
      responseType,
    );
    await createTransactionService().responseTransactionProjection(response);
    await createTransactionService().transactionFeeAffected({
      transactionUuid: txn.transactionUuid,
      fees: { value: String(amount * 0.014), currency: 'AUD' },
    } as any);
    await createTransactionService().transactionNotesProjection({
      transactionUuid: txn.transactionUuid,
      notes: uuidv4(),
    } as any);
    return txn;
  };

  /**
   * create transaction per hour between 2020-12-21 to 2021-01-01
   */
  beforeAll(async () => {
    when(mockCardholderService.getCardholderData).thenReturn(() => Promise.resolve({}));
    mockQueryCustomerSiteUuids.mockResolvedValue([]);
    (S3Client as any).mockImplementation(() => ({
      upload: mockUpload,
      getSignedUrl: mockGetSignedUrl,
    }));
    filter = {
      expressionValues: {
        ':timestampFilter_between_start': { S: new Date(startDate.getTime() - 1000).toISOString() },
        ':timestampFilter_between_end': {
          S: new Date(endDate.getTime() + 1000 * 60 * 60 * 24 * 356).toISOString(),
        },
      },
    };

    while (startDate.getTime() < endDate.getTime()) {
      const responseType =
        startDate.getHours() === 1 ? TransactionResponseType.DECLINED : TransactionResponseType.APPROVED;
      const cardMedia = startDate.getHours() === 1 ? CardMedia.MANUAL : CardMedia.PICC;
      await createResponseTransaction(
        entityUuid,
        siteUuid,
        deviceUuid,
        purchaseAmount,
        tip,
        surcharge,
        tax,
        startDate.toISOString(),
        startDate.toISOString(),
        responseType,
        cardMedia,
      );
      startDate.setHours(startDate.getHours() + 1);
    }
  }, 100000);

  beforeEach(() => {
    txnService = createTransactionService();
    txnExportService = createExportService();
    mockUpload.mockReset();
    mockUpload.mockReturnValue({});
    mockGetSignedUrl.mockReset();
  });

  it('should be able to upload to s3', async () => {
    const result = await txnExportService.uploadToS3(new S3Client(), '', Buffer.from(''));
    expect(result).not.toBeNull();
  });

  it('should return empty array if no transaction found', async () => {
    mockQueryCustomerSiteUuids.mockResolvedValue([]);
    const { transactions } = await createTransactionService().getTransactions(
      { timestampFilter: JSON.stringify(filter) },
      entityUuid,
    );
    await createExportService().exportTransaction(
      { timestampFilter: JSON.stringify(filter) },
      entityUuid,
      ExportType.CSV,
      'managerCustomerUuid',
    );
    expect(mockUpload).toHaveBeenCalledTimes(1);
    expect(mockUpload.mock.calls[0][0].Bucket).toBe('bucket');
    const body = (mockUpload.mock.calls[0][0].Body as Buffer).toString();
    compareCsvTransactionReports(body, transactions, 'Z', 0);
    expect(mockGetSignedUrl).toHaveBeenCalledTimes(1);
  });

  it('should be able to generate csv with UTC timezone', async () => {
    const { transactions } = await createTransactionService().getTransactions(
      { timestampFilter: JSON.stringify(filter) },
      entityUuid,
    );
    await createExportService().exportTransaction(
      { timestampFilter: JSON.stringify(filter) },
      entityUuid,
      ExportType.CSV,
    );
    expect(mockUpload).toHaveBeenCalledTimes(1);
    expect(mockUpload.mock.calls[0][0].Bucket).toBe('bucket');
    const body = (mockUpload.mock.calls[0][0].Body as Buffer).toString();
    compareCsvTransactionReports(body, transactions, 'Z', 288);
    expect(mockGetSignedUrl).toHaveBeenCalledTimes(1);
  });

  it('should be able to generate csv with UTC timezone with customer manager sites', async () => {
    mockQueryCustomerSiteUuids.mockResolvedValue([siteUuid]);
    const { transactions } = await createTransactionService().getTransactions(
      { timestampFilter: JSON.stringify(filter) },
      entityUuid,
    );
    await createExportService().exportTransaction(
      { timestampFilter: JSON.stringify(filter) },
      entityUuid,
      ExportType.CSV,
      'managerCustomerUuid',
    );
    expect(mockUpload).toHaveBeenCalledTimes(1);
    expect(mockUpload.mock.calls[0][0].Bucket).toBe('bucket');
    const body = (mockUpload.mock.calls[0][0].Body as Buffer).toString();
    compareCsvTransactionReports(body, transactions, 'Z', 288);
    expect(mockGetSignedUrl).toHaveBeenCalledTimes(1);
  });

  it('should be able to generate csv with specified timezone', async () => {
    const start = new Date('2020-12-20T00:00:00.000Z');
    const end = new Date(endDate.getTime() + 1000 * 60 * 60 * 24 * 356);
    const timestampFilter = {
      expressionValues: {
        ':timestampFilter_between_start': { S: start.toISOString().replace('Z', '+10:00') },
        ':timestampFilter_between_end': {
          S: end.toISOString().replace('Z', '+10:00'),
        },
      },
    };
    const { transactions } = await createTransactionService().getTransactions(
      { timestampFilter: JSON.stringify(timestampFilter) },
      entityUuid,
    );
    await createExportService().exportTransaction(
      { timestampFilter: JSON.stringify(timestampFilter) },
      entityUuid,
      ExportType.CSV,
    );
    expect(mockUpload).toHaveBeenCalledTimes(1);
    expect(mockUpload.mock.calls[0][0].Bucket).toBe('bucket');
    const body = (mockUpload.mock.calls[0][0].Body as Buffer).toString();
    compareCsvTransactionReports(body, transactions, '+10:00', 288);
    expect(mockGetSignedUrl).toHaveBeenCalledTimes(1);
  });

  it('should be able to generate xlsx with UTC timezone', async () => {
    const { transactions } = await createTransactionService().getTransactions(
      { timestampFilter: JSON.stringify(filter) },
      entityUuid,
    );
    await createExportService().exportTransaction(
      { timestampFilter: JSON.stringify(filter) },
      entityUuid,
      ExportType.XLSX,
    );
    expect(mockUpload).toHaveBeenCalledTimes(1);
    expect(mockUpload.mock.calls[0][0].Bucket).toBe('bucket');
    const body = mockUpload.mock.calls[0][0].Body as Buffer;
    await compareXlsxTransactionReports(body, transactions, 'Z', 288);
    expect(mockGetSignedUrl).toHaveBeenCalledTimes(1);
  });

  it('should be able to generate xlsx with UTC timezone with customer manager sites', async () => {
    mockQueryCustomerSiteUuids.mockResolvedValue([siteUuid]);
    const { transactions } = await createTransactionService().getTransactions(
      { timestampFilter: JSON.stringify(filter) },
      entityUuid,
    );
    await createExportService().exportTransaction(
      { timestampFilter: JSON.stringify(filter) },
      entityUuid,
      ExportType.XLSX,
      'managerCustomerUuid',
    );
    expect(mockUpload).toHaveBeenCalledTimes(1);
    expect(mockUpload.mock.calls[0][0].Bucket).toBe('bucket');
    const body = mockUpload.mock.calls[0][0].Body as Buffer;
    await compareXlsxTransactionReports(body, transactions, 'Z', 288);
    expect(mockGetSignedUrl).toHaveBeenCalledTimes(1);
  });

  it('should be able to generate xlsx with specified timezone', async () => {
    const start = new Date('2020-12-20T00:00:00.000Z');
    const end = new Date(endDate.getTime() + 1000 * 60 * 60 * 24 * 356);
    const timestampFilter = {
      expressionValues: {
        ':timestampFilter_between_start': { S: start.toISOString().replace('Z', '+10:00') },
        ':timestampFilter_between_end': {
          S: end.toISOString().replace('Z', '+10:00'),
        },
      },
    };
    const { transactions } = await createTransactionService().getTransactions(
      { timestampFilter: JSON.stringify(timestampFilter) },
      entityUuid,
    );
    await createExportService().exportTransaction(
      { timestampFilter: JSON.stringify(timestampFilter) },
      entityUuid,
      ExportType.XLSX,
    );
    expect(mockUpload).toHaveBeenCalledTimes(1);
    expect(mockUpload.mock.calls[0][0].Bucket).toBe('bucket');
    const body = mockUpload.mock.calls[0][0].Body as Buffer;
    await compareXlsxTransactionReports(body, transactions, '+10:00', 288);
    expect(mockGetSignedUrl).toHaveBeenCalledTimes(1);
  });

  it('should be able to generate pdf', async () => {
    const spy = jest.spyOn(pdfService, 'createDocument');
    const timestampFilter = JSON.stringify(filter);
    await createTransactionService().getTransactions({ timestampFilter }, entityUuid);
    await createExportService().exportTransaction({ timestampFilter }, entityUuid, ExportType.PDF);
    expect(mockUpload).toHaveBeenCalledTimes(1);
    expect(mockUpload.mock.calls[0][0].Bucket).toBe('bucket');

    const body = mockUpload.mock.calls[0][0].Body as Readable;
    expect(mockGetSignedUrl).toHaveBeenCalledTimes(1);

    const [start, end] = getStartAndEndFromFilter(timestampFilter);
    expect(spy.mock.calls[0][0].title).toEqual(
      `Transactions for ${dateTimeFormatForOffset(start, 'Z', true)} – ${dateTimeFormatForOffset(end, 'Z', true)}`,
    );

    const writeStream = fs.createWriteStream(path.join(baseDir, 'transactionsTest.pdf'));
    body.pipe(writeStream);
    await new Promise((resolve) => {
      body.on('end', resolve);
    });
  }, 120000);

  it('should throw server error if unsupported type is passed in', async () => {
    await expect(
      createExportService().exportTransaction({}, entityUuid, 'Unsupported Type' as any),
    ).rejects.toThrowError('Failed to upload transaction.');
    expect(mockUpload).toHaveBeenCalledTimes(0);
    expect(mockGetSignedUrl).toHaveBeenCalledTimes(0);
  });

  it('should be able to catch error if upload failed', async () => {
    mockUpload.mockRejectedValue(() => {
      throw new Error();
    });
    await expect(
      createExportService().exportTransaction({ timestampFilter: JSON.stringify({}) }, entityUuid, ExportType.CSV),
    ).rejects.toThrowError(Error);
    expect(mockUpload).toHaveBeenCalledTimes(1);
  });

  it('should be able to generate csv with settlement when transaction is deposited', async () => {
    const originalFilter = { ...filter };
    const depositedTxnTotal = 5;
    const depositedDate = new Date(new Date().getTime() - 1000 * 60 * 60 * 24 * 1);
    const depositId = entityUuid + siteUuid + depositedDate.toISOString();
    const depositedDate2 = new Date(new Date().getTime() - 1100 * 60 * 60 * 24 * 1);
    const depositId2 = entityUuid + siteUuid + depositedDate2.toISOString();
    await dynamodbSrv.save(
      'Entities',
      {
        id: depositId,
        type: `${DbRecordType.DEPOSIT}${new Date().getTime()}`,
      },
      {
        shortId,
        depositUuid: depositId,
        entityUuid,
        completedAtUtc: depositedDate2.toISOString(),
      },
      {},
    );
    await dynamodbSrv.save(
      'Entities',
      {
        id: depositId2,
        type: `${DbRecordType.DEPOSIT}${new Date().getTime()}`,
      },
      {
        depositUuid: depositId2,
        entityUuid,
        completedAtUtc: depositedDate2.toISOString(),
        shortId,
      },
      {},
    );

    filter = {
      expressionValues: {
        ':timestampFilter_between_start': {
          S: new Date(depositedDate.getTime() - 1000 * 60 * 60 * 24 * 7).toISOString(),
        },
        ':timestampFilter_between_end': {
          S: new Date().toISOString(),
        },
      },
    };
    // create deposited transactions
    for (let index = 0; index < depositedTxnTotal; index += 1) {
      const timestamp = new Date(depositedDate.getTime() - 1000 * 60 * 60 * 24 * (index + 1)).toISOString();
      const txn = await createResponseTransaction(
        entityUuid,
        siteUuid,
        deviceUuid,
        purchaseAmount,
        tip,
        surcharge,
        tax,
        timestamp,
        timestamp,
        TransactionResponseType.APPROVED,
        CardMedia.MANUAL,
      );
      if (index < 3) {
        await txnService.transactionDepositedProjection({ transactionUuid: txn.transactionUuid, depositId });
      }
      if (index === 3) {
        await txnService.transactionDepositedProjection({
          transactionUuid: txn.transactionUuid,
          depositId: depositId2,
        });
      }
      if (index === 4) {
        await txnService.transactionDepositedProjection({
          transactionUuid: txn.transactionUuid,
          depositId: uuidv4(),
        });
      }
    }

    const { transactions } = await txnService.getTransactions({ timestampFilter: JSON.stringify(filter) }, entityUuid);
    await createExportService().exportTransaction(
      { timestampFilter: JSON.stringify(filter) },
      entityUuid,
      ExportType.CSV,
    );
    expect(mockUpload).toHaveBeenCalledTimes(1);
    expect(mockUpload.mock.calls[0][0].Bucket).toBe('bucket');
    const body = (mockUpload.mock.calls[0][0].Body as Buffer).toString();
    compareCsvTransactionReports(body, transactions, 'Z', depositedTxnTotal);
    expect(mockGetSignedUrl).toHaveBeenCalledTimes(1);

    filter = originalFilter;
  });
});
