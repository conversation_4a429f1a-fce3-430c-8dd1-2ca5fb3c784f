import {
  AwsRegions,
  Domicile,
  getDefaultDomicileFromRegion,
  getDomicileFromISOAlpha2CountryCode,
  getRegionForDomicile,
  getSupportedDomiciles,
} from './domicile';

describe('Domicile', () => {
  it('should return supported domiciles for Sydney', () => {
    expect(getSupportedDomiciles(AwsRegions.SYDNEY)).toEqual([Domicile.AU]);
  });

  it('should return supported domiciles for London', () => {
    expect(getSupportedDomiciles(AwsRegions.LONDON)).toEqual([Domicile.GB]);
  });

  describe('getDomicileFromISOAlpha2CountryCode', () => {
    it('should return AUS for AU', () => {
      expect(getDomicileFromISOAlpha2CountryCode('AU')).toEqual(Domicile.AU);
    });

    it('should return GBR for GB', () => {
      expect(getDomicileFromISOAlpha2CountryCode('GB')).toEqual(Domicile.GB);
    });

    it('should return AUS for CC', () => {
      expect(getDomicileFromISOAlpha2CountryCode('CC')).toEqual(Domicile.AU);
    });

    it('should return AUS for CX', () => {
      expect(getDomicileFromISOAlpha2CountryCode('CX')).toEqual(Domicile.AU);
    });

    it('should return AUS for NF', () => {
      expect(getDomicileFromISOAlpha2CountryCode('NF')).toEqual(Domicile.AU);
    });

    it('should return undefined for unknown country code', () => {
      expect(getDomicileFromISOAlpha2CountryCode('XX')).toBeUndefined();
    });
  });

  describe('getRegionForDomicile', () => {
    it('should return Sydney for AU', () => {
      expect(getRegionForDomicile('AUS')).toEqual(AwsRegions.SYDNEY);
    });

    it('should return London for GB', () => {
      expect(getRegionForDomicile('GBR')).toEqual(AwsRegions.LONDON);
    });
  });

  describe('getDefaultDomicileFromRegion', () => {
    it('should return AU for Sydney region', () => {
      expect(getDefaultDomicileFromRegion(AwsRegions.SYDNEY)).toEqual(Domicile.AU);
    });

    it('should return GB for London region', () => {
      expect(getDefaultDomicileFromRegion(AwsRegions.LONDON)).toEqual(Domicile.GB);
    });

    it('should throw an error for unsupported region', () => {
      expect(() => getDefaultDomicileFromRegion('unsupported-region')).toThrowError(
        'Unsupported domicile: unsupported-region',
      );
    });
  });
});
