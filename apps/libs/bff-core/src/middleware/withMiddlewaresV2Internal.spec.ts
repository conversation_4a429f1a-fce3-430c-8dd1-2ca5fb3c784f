import { ZellerComponent } from '../types/zellerComponent';
import { AwsRegions, Domicile } from '../utils/domicile';

import { DomicileContext } from './domicileContext';
import { PermissionRoot } from './lambdaMetadata';
import { domicileMiddleware, getDomicile } from './withMiddlewaresV2Internal';

jest.mock('./accessControlMiddlewareSinglePermissionRoot');

const getEnvServiceMock = jest.fn();

jest.mock('../config/envService', () => {
  const originalModule = jest.requireActual('../config/envService');
  return {
    ...originalModule,
    getEnvService: () => getEnvServiceMock(),
  };
});

const getDomicileValueMock = jest.fn();
const getAccountIdMock = jest.fn();

jest.mock('../domicile', () => {
  return {
    DomicileLookupDb: jest.fn().mockImplementation(() => {
      return {
        getDomicileValue: (args: any) => getDomicileValueMock(args),
        getAccountId: (args: any) => getAccountIdMock(args),
      };
    }),
  };
});

const invokeMock = jest.fn();
jest.mock('../lambda', () => {
  return {
    LambdaService: jest.fn().mockImplementation(() => {
      return {
        invoke: (event: any) => invokeMock(event),
      };
    }),
  };
});

describe('getDomicile', () => {
  const mockEnvService = {
    domicileLookupEnabled: true,
    awsRegion: AwsRegions.SYDNEY,
  };

  beforeEach(() => {
    getEnvServiceMock.mockReturnValue(mockEnvService as any);
    getDomicileValueMock.mockReset();
    invokeMock.mockReset();
  });

  it('should return domicile when domicileLookupEnabled is true and permissionsDerivedFrom is ENTITY_ROLE', async () => {
    const config = {
      permissionsDerivedFrom: PermissionRoot.ENTITY_ROLE,
    } as any;

    const event = {
      args: {
        entityUuid: 'test-entity-uuid',
      },
    } as any;

    getDomicileValueMock.mockResolvedValue('test-domicile');

    const result = await getDomicile(config.permissionsDerivedFrom, event);

    expect(getDomicileValueMock).toHaveBeenCalledWith({ entityUuid: 'test-entity-uuid' });
    expect(result).toBe('test-domicile');
  });

  it('should return domicile when domicileLookupEnabled is true and permissionsDerivedFrom is CUSTOMER_ROLE', async () => {
    const config = {
      permissionsDerivedFrom: PermissionRoot.CUSTOMER_OWN_DATA,
    } as any;

    const event = {
      args: {
        customerUuid: 'test-customer-uuid',
      },
    } as any;

    getDomicileValueMock.mockResolvedValue('test-domicile');

    const result = await getDomicile(config.permissionsDerivedFrom, event);

    expect(getDomicileValueMock).toHaveBeenCalledWith({ customerUuid: 'test-customer-uuid' });
    expect(result).toBe('test-domicile');
  });

  it('should use the first entityUuid from args when multiple are provided', async () => {
    mockEnvService.domicileLookupEnabled = true;
    const config = {
      permissionsDerivedFrom: PermissionRoot.MULTIPLE_ENTITY_ROLE,
    } as any;
    const event = {
      args: {
        entityUuids: ['test-entity-uuid-1', 'test-entity-uuid-2'],
      },
    } as any;
    getDomicileValueMock.mockResolvedValue('test-domicile');
    const result = await getDomicile(config.permissionsDerivedFrom, event);
    expect(getDomicileValueMock).toHaveBeenCalledWith({
      entityUuid: 'test-entity-uuid-1',
    });
    expect(result).toBe('test-domicile');
  });

  it('should handle missing UUID gracefully', async () => {
    mockEnvService.domicileLookupEnabled = true;
    const config = {
      permissionsDerivedFrom: PermissionRoot.ENTITY_ROLE,
    } as any;

    const event = {
      args: {},
    } as any;

    getDomicileValueMock.mockResolvedValue(null);

    const result = await getDomicile(config.permissionsDerivedFrom, event);

    expect(getDomicileValueMock).not.toHaveBeenCalled();
    expect(result).toBeUndefined();
  });

  it('should not call getDomicileValue when uuid doesnt exist', async () => {
    const config = {
      permissionsDerivedFrom: PermissionRoot.ENTITY_ROLE,
    } as any;

    const event = {
      args: {},
    } as any;

    getDomicileValueMock.mockResolvedValue(null);

    const result = await getDomicile(config.permissionsDerivedFrom, event);

    expect(getDomicileValueMock).not.toHaveBeenCalled();
    expect(result).toBeUndefined();
  });

  it('should be able to get arn for remote lambda in different region', async () => {
    getDomicileValueMock.mockResolvedValue(Domicile.GB);
    getAccountIdMock.mockReturnValue('************');
    DomicileContext.getInstance().setDomicile(null);
    const event = {
      args: {
        entityUuid: 'test-entity-uuid',
      },
    };
    await domicileMiddleware({ metadata: { component: ZellerComponent.MP } } as any)(
      event as any,
      { functionName: 'mockFunction' } as any,
      async () => {
        // it shouldn't come here.
        expect(false).toBeTruthy();
      },
    );
    expect(invokeMock).toHaveBeenCalledWith(`aws:lambda:${AwsRegions.LONDON}:************:function:mockFunction`);
  });

  it('should not invoke remote lambda if domicile is not found', async () => {
    getDomicileValueMock.mockResolvedValue(null);
    getAccountIdMock.mockResolvedValue('************');
    DomicileContext.getInstance().setDomicile(null);
    const event = {
      args: {
        entityUuid: 'test-entity-uuid',
      },
    };
    let callbackIsCalled = false;
    await domicileMiddleware({ metadata: { component: ZellerComponent.MP } } as any)(
      event as any,
      { functionName: 'mockFunction' } as any,
      async () => {
        expect(invokeMock).not.toHaveBeenCalled();
        callbackIsCalled = true;
      },
    );
    expect(callbackIsCalled).toBeTruthy();
  });
});
