import type { Callback, Context } from 'aws-lambda';

import { type EnvironmentService, getEnvService } from '../config';
import { DomicileLookupDb } from '../domicile';
import { LambdaService } from '../lambda';
import type { EventType } from '../types/eventType';
import type { LambdaEventSource } from '../types/lambdaEventSource';
import { isAccessControlMiddlewareRequiredForEventSource } from '../types/lambdaEventSource';
import { ZellerComponent } from '../types/zellerComponent';
import { safeGetAggregateId } from '../utils/aggregateId';
import { Domicile, getRegionForDomicile, getSupportedDomiciles } from '../utils/domicile';
import { debug, error, info, setLoggerAggregateId, warn } from '../utils/logger';

import { type ZellerApiEvent, accessControlMiddleware } from './accessControlMiddleware';
import type {
  CustomerSpecificEvent,
  EntitySpecificEvent,
  MpMultiEntityAccessCheckEvent,
} from './accessControlMiddleware/accessCheckRequest';
import {
  getUnvalidatedCustomerUuidFromArgs,
  getUnvalidatedEntityUuidFromArgs,
} from './accessControlMiddleware/getUnvalidatedUuidFromArgs';
import { isMerchantFacingApiComponent } from './accessControlMiddleware/merchantFacingApiComponent';
import { DomicileContext } from './domicileContext';
import type { IErrorHandler } from './errorHandlerModules';
import type { IHandler } from './iHandler';
import type { IMiddleware } from './iMiddleware';
import type { IPostMiddleware } from './iPostMiddleware';
import {
  PermissionRoot,
  isMultiplePermissionRoot,
  isSinglePermissionRoot,
  type LambdaMetadata,
} from './lambdaMetadata';
import { logMiddleware } from './logMiddleware';
import type { MiddlewareConfig } from './middlewareConfig';
import type { CallbackResponse, EntityCustomerContext } from './types';
import { withMiddlewaresBaseV2Internal } from './withMiddlewaresBaseV2Internal';
import { xrayAggregateMiddleware } from './xrayAggregateMiddleware';
import { xrayTraceIdMiddleware } from './xrayTraceIdMiddleware';

type InternalMiddlewareConfig<
  TComponentName extends ZellerComponent,
  TLambdaType extends LambdaEventSource,
  TEvent extends EventType<TLambdaType>,
  TResponse = any,
  TContext extends Context = any,
> = {
  metadata: LambdaMetadata<TComponentName, TLambdaType>;
  handler: IHandler<TEvent, TResponse | CallbackResponse, TContext>;
  middleware: IMiddleware<NoInfer<TEvent>, TContext>[];
  postMiddlewares: IPostMiddleware<TResponse, TContext>[];
  errorHandler: IErrorHandler;
} & MiddlewareConfig<TResponse, TContext>;

export const getDomicile = async (permissionsDerivedFrom: PermissionRoot, event: ZellerApiEvent | ZellerApiEvent[]) => {
  const envSettings: EnvironmentService = getEnvService();
  const uuid: { customerUuid?: string; entityUuid?: string } = {};
  if (isSinglePermissionRoot(permissionsDerivedFrom)) {
    if ([PermissionRoot.ENTITY_ROLE, PermissionRoot.DEVICE_ENTITY_ROLE].includes(permissionsDerivedFrom)) {
      uuid.entityUuid = getUnvalidatedEntityUuidFromArgs(event as EntitySpecificEvent);
    } else {
      uuid.customerUuid = getUnvalidatedCustomerUuidFromArgs(event as CustomerSpecificEvent);
    }
  } else if (
    isMultiplePermissionRoot(permissionsDerivedFrom) &&
    (event as MpMultiEntityAccessCheckEvent)?.args?.entityUuids.length > 0
  ) {
    uuid.entityUuid = (event as MpMultiEntityAccessCheckEvent).args.entityUuids[0];
  }
  if (!uuid.customerUuid && !uuid.entityUuid) {
    debug('No customer or entity uuid found in event');
    return undefined;
  }
  const domicileLookup = new DomicileLookupDb(envSettings);
  const domicile = await domicileLookup.getDomicileValue(uuid);
  if (domicile) {
    DomicileContext.getInstance().setDomicile(domicile);
  }
  return domicile;
};

const getPermissionRoot = (config: any) =>
  config.metadata.component === ZellerComponent.DBS || config.metadata.component === ZellerComponent.SDK
    ? PermissionRoot.DEVICE_ENTITY_ROLE
    : PermissionRoot.ENTITY_ROLE;

const invokeRemoteLambda = async (
  envSettings: EnvironmentService,
  domicile: string,
  event: ZellerApiEvent | ZellerApiEvent[],
  context: EntityCustomerContext,
) => {
  const domiciles = getSupportedDomiciles(envSettings.awsRegion);
  if (!domiciles.includes(Domicile[domicile as keyof typeof Domicile])) {
    const region = getRegionForDomicile(domicile);
    const domicileLookup = new DomicileLookupDb(envSettings);
    const accountId = await domicileLookup.getAccountId(region);
    const remoteLambdaArn = `aws:lambda:${region}:${accountId}:function:${context.functionName}`;
    info(`Invoke remote lambda ${remoteLambdaArn} in region ${region} for domicile ${domicile}`);
    try {
      const lambdaService = new LambdaService(envSettings);
      await lambdaService.invoke(remoteLambdaArn, event);
    } catch (err) {
      error(`Failed to invoke remote lambda ${remoteLambdaArn} for domicile ${domicile}: ${err}`);
    }
  }
};

export const domicileMiddleware = function MiddlewareFn<
  TComponentName extends ZellerComponent,
  TLambdaType extends LambdaEventSource,
  TEvent extends EventType<TLambdaType>,
  TResponse,
  TContext extends Context = any,
>(config: InternalMiddlewareConfig<TComponentName, TLambdaType, TEvent, TResponse, TContext>) {
  return async (event: ZellerApiEvent | ZellerApiEvent[], context: EntityCustomerContext, next: any) => {
    const envSettings: EnvironmentService = getEnvService();
    if (
      config.metadata.component &&
      envSettings.domicileLookupEnabled &&
      !DomicileContext.getInstance().getDomicile()
    ) {
      const permissionRoot = getPermissionRoot(config);
      const domicile = await getDomicile(config.metadata.permissionsDerivedFrom ?? permissionRoot, event);
      context.domicile = domicile;
      if (domicile) {
        return invokeRemoteLambda(envSettings, domicile, event, context);
      }
      warn(
        `Domicile not found for event: ${JSON.stringify(
          event,
        )}. Ensure the domicile lookup is enabled and the event contains valid UUIDs.`,
      );
    }
    return next(event, context);
  };
};

export const withMiddlewaresV2Internal =
  <
    TComponentName extends ZellerComponent,
    TLambdaType extends LambdaEventSource,
    TEvent extends EventType<TLambdaType>,
    TResponse = any,
    TContext extends Context = any,
  >(
    config: InternalMiddlewareConfig<TComponentName, TLambdaType, TEvent, TResponse, TContext>,
  ) =>
  (event: TEvent, context: TContext, callback: Callback<TResponse | CallbackResponse>) => {
    if (event && (event as any).source && (event as any).source === config.keepWarmSource) {
      debug('Keep alive');
      callback(null, 'Keep alive');
      return;
    }

    let thisMiddleware = config.middleware;
    const thisPostMiddleware = config.postMiddlewares;
    if (config.logging) {
      thisMiddleware = [logMiddleware(config.loggingMaskRules), ...thisMiddleware];
    }

    thisMiddleware = [xrayTraceIdMiddleware, ...thisMiddleware];

    if (config.getAggregateId) {
      const aggregateId = safeGetAggregateId(event, context, config.getAggregateId);
      if (aggregateId) {
        setLoggerAggregateId(aggregateId);
        thisMiddleware = [xrayAggregateMiddleware(config.getAggregateId), ...thisMiddleware];
      }
    }

    if (
      isMerchantFacingApiComponent(config.metadata.component) &&
      isAccessControlMiddlewareRequiredForEventSource(config.metadata.eventType)
    ) {
      thisMiddleware = [
        // the validate status will update to true in the future
        accessControlMiddleware({
          component: config.metadata.component,
          isUnauthenticatedAccessAllowed: config.metadata.isUnauthenticatedAccessAllowed,
          permissionsDerivedFrom: config.metadata.permissionsDerivedFrom,
          eventType: config.metadata.eventType,
        }),
        ...thisMiddleware,
      ];
    }
    thisMiddleware = [domicileMiddleware(config), ...thisMiddleware];

    withMiddlewaresBaseV2Internal({ ...config, middlewares: thisMiddleware, postMiddlewares: thisPostMiddleware })(
      event,
      context,
      callback,
    );
  };
